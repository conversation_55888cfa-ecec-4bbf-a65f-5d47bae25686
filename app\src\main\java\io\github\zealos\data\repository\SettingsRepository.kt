package io.github.zealos.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class SettingsRepository(private val dataStore: DataStore<Preferences>) {

    private object PreferencesKeys {
        val THEME = stringPreferencesKey("theme")
        val DYNAMIC_THEME = booleanPreferencesKey("dynamic_theme")
    }

    val theme: Flow<String> = dataStore.data.map {
        it[PreferencesKeys.THEME] ?: "System"
    }

    val dynamicTheme: Flow<Boolean> = dataStore.data.map {
        it[PreferencesKeys.DYNAMIC_THEME] ?: true
    }

    suspend fun setTheme(theme: String) {
        dataStore.edit {
            it[PreferencesKeys.THEME] = theme
        }
    }

    suspend fun setDynamicTheme(dynamicTheme: Boolean) {
        dataStore.edit {
            it[PreferencesKeys.DYNAMIC_THEME] = dynamicTheme
        }
    }
}