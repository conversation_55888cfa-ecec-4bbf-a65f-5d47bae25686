package io.github.zealos

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.navigation.compose.rememberNavController
import io.github.zealos.ui.navigation.NavGraph
import io.github.zealos.ui.theme.ZealOSTheme

import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import io.github.zealos.ui.screens.settings.SettingsViewModel
import org.koin.androidx.compose.koinViewModel

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            val settingsViewModel: SettingsViewModel = koinViewModel()
            val theme by settingsViewModel.theme.collectAsState()
            val dynamicTheme by settingsViewModel.dynamicTheme.collectAsState()
            ZealOSTheme(
                darkTheme = when (theme) {
                    "Light" -> false
                    "Dark" -> true
                    else -> resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK == android.content.res.Configuration.UI_MODE_NIGHT_YES
                },
                dynamicColor = dynamicTheme
            ) {
                val navController = rememberNavController()
                NavGraph(navController = navController)
            }
        }
    }
}
