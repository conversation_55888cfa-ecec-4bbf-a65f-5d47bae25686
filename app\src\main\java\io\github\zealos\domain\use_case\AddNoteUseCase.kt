package io.github.zealos.domain.use_case

import io.github.zealos.data.local.Note
import io.github.zealos.data.repository.NoteRepository
import kotlinx.datetime.Clock

class AddNoteUseCase(private val repository: NoteRepository) {

    suspend operator fun invoke(note: Note) {
        if (note.title.isBlank() || note.content.isBlank()) {
            throw IllegalArgumentException("The title and content of the note can't be empty.")
        }
        repository.insertNote(note.copy(updatedAt = Clock.System.now()))
    }
}
