package io.github.zealos.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import io.github.zealos.ui.screens.add_edit_note.AddEditNoteScreen
import io.github.zealos.ui.screens.notes.NotesScreen
import kotlinx.serialization.json.Json

import io.github.zealos.ui.screens.settings.SettingsScreen

@Composable
fun NavGraph(navController: NavHostController) {
    NavHost(
        navController = navController,
        startDestination = NotesScreen
    ) {
        composable<NotesScreen> {
            NotesScreen(navController)
        }
        composable<AddEditNoteScreen> {
            AddEditNoteScreen(navController)
        }
        composable<SettingsScreen> {
            SettingsScreen()
        }
    }
}
