package io.github.zealos.ui.screens.add_edit_note

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.github.zealos.data.local.Note
import io.github.zealos.domain.use_case.NoteUseCases
import io.github.zealos.util.UiEvent
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

class AddEditNoteViewModel(
    private val noteUseCases: NoteUseCases,
    private val savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val _note = MutableStateFlow<Note?>(null)
    val note = _note.asStateFlow()

    private val _title = MutableStateFlow("")
    val title = _title.asStateFlow()

    private val _content = MutableStateFlow("")
    val content = _content.asStateFlow()

    private val _color = MutableStateFlow(0)
    val color = _color.asStateFlow()

    private val _eventFlow = MutableSharedFlow<UiEvent>()
    val eventFlow = _eventFlow.asSharedFlow()

    private var noteId: Int? = null

    init {
        savedStateHandle.get<Int>("noteId")?.let {
            if (it != -1) {
                noteId = it
                viewModelScope.launch {
                    noteUseCases.getNote(it)?.let {
                        _note.value = it
                        _title.value = it.title
                        _content.value = it.content
                        _color.value = it.color
                    }
                }
            }
        }
    }

    fun onTitleChange(title: String) {
        _title.value = title
    }

    fun onContentChange(content: String) {
        _content.value = content
    }

    fun onColorChange(color: Int) {
        _color.value = color
    }

    fun saveNote() {
        viewModelScope.launch {
            try {
                noteUseCases.addNote(
                    Note(
                        id = noteId ?: 0,
                        title = title.value,
                        content = content.value,
                        createdAt = _note.value?.createdAt ?: Clock.System.now(),
                        updatedAt = Clock.System.now(),
                        color = color.value
                    )
                )
                _eventFlow.emit(UiEvent.SaveNote)
            } catch (e: IllegalArgumentException) {
                _eventFlow.emit(
                    UiEvent.ShowSnackbar(
                        message = e.message ?: "Couldn't save note"
                    )
                )
            }
        }
    }
}
