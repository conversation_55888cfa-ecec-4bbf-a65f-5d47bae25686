package io.github.zealos.di

import androidx.lifecycle.SavedStateHandle
import androidx.room.Room
import io.github.zealos.data.local.MIGRATION_1_2
import io.github.zealos.data.local.NoteDatabase
import io.github.zealos.data.repository.NoteRepository
import io.github.zealos.data.repository.NoteRepositoryImpl
import io.github.zealos.domain.use_case.*
import io.github.zealos.ui.screens.add_edit_note.AddEditNoteViewModel
import io.github.zealos.ui.screens.notes.NotesViewModel
import org.koin.android.ext.koin.androidApplication
import io.github.zealos.ui.screens.settings.SettingsViewModel
import io.github.zealos.data.repository.SettingsRepository
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.PreferenceDataStoreFactory
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStoreFile
import org.koin.android.ext.koin.androidContext
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module

val appModule = module {
    single {
        Room.databaseBuilder(
            androidApplication(),
            NoteDatabase::class.java,
            NoteDatabase.DATABASE_NAME
        ).addMigrations(MIGRATION_1_2)
            .build()
    }

    single<NoteRepository> {
        NoteRepositoryImpl(get<NoteDatabase>().noteDao)
    }


    single {
        NoteUseCases(
            getNotes = GetNotesUseCase(get()),
            deleteNote = DeleteNoteUseCase(get()),
            addNote = AddNoteUseCase(get()),
            getNote = GetNoteUseCase(get())
        )
    }

    viewModel {
        NotesViewModel(get())
    }

    single<DataStore<Preferences>> {
        PreferenceDataStoreFactory.create {
            androidContext().preferencesDataStoreFile("settings")
        }
    }

    single {
        SettingsRepository(get())
    }

    viewModel { (handle: SavedStateHandle) ->
        AddEditNoteViewModel(get(), handle)
    }

    viewModel {
        SettingsViewModel(get())
    }
}