package io.github.zealos.data.local

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.Instant

import android.content.ContentValues

@Entity(tableName = "notes")
data class Note(
    @PrimaryKey(autoGenerate = true)
    val id: Int = 0,
    val title: String,
    val content: String,
    val createdAt: Instant,
    val updatedAt: Instant,
    val color: Int = 0
) {
    companion object {
        fun fromContentValues(values: ContentValues): Note {
            return Note(
                id = values.getAsInteger("id"),
                title = values.getAsString("title"),
                content = values.getAsString("content"),
                createdAt = Instant.fromEpochMilliseconds(values.getAsLong("createdAt")),
                updatedAt = Instant.fromEpochMilliseconds(values.getAsLong("updatedAt")),
                color = values.getAsInteger("color")
            )
        }
    }
}
